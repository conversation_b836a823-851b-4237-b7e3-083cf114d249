import './globals.css';
import type { ReactNode } from 'react';
import TRPCProvider from '@/lib/trpc/provider';

export const metadata = {
  title: 'Estimates App',
  description: 'Paving estimates powered by tRPC',
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body>
        <TRPCProvider>{children}</TRPCProvider>
      </body>
    </html>
  );
}
