'use client';

import { ReactNode } from 'react';
import { trpc, getClient } from './client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

export default function TRPCProvider({ children }: { children: ReactNode }) {
  const client = getClient();
  const queryClient = new QueryClient();

  return (
    <trpc.Provider client={client} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </trpc.Provider>
  );
}
