import { createTRPCRouter, publicProcedure } from "../init";
import { db } from "@/lib/db";
import { estimateSchema } from "@/lib/schemas";

export const estimateRouter = createTRPCRouter({
  getAll: publicProcedure.query(async () => {
    return await db.estimate.findMany({
      include: { items: true },
      orderBy: { createdAt: "desc" },
    });
  }),

  create: publicProcedure.input(estimateSchema).mutation(async ({ input }) => {
    return await db.estimate.create({
      data: {
        jobNumber: input.jobNumber,
        customer: input.customer,
        address: input.address,
        items: {
          create: input.items,
        },
      },
      include: { items: true },
    });
  }),
});
