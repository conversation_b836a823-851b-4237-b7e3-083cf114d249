import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../init";
import { db } from "@/lib/db";

export const estimateRouter = createTRPCRouter({
  getAll: publicProcedure.query(async () => {
    return await db.estimate.findMany({
      include: { items: true },
      orderBy: { createdAt: "desc" },
    });
  }),

  create: publicProcedure
    .input(
      z.object({
        jobNumber: z.string().min(1, "Job number is required"),
        customer: z.string().min(1, "Customer is required"),
        address: z.string(),
        items: z
          .array(
            z.object({
              type: z.string(),
              item: z.string(),
              units: z.number(),
              time: z.number().optional(),
              rate: z.number(),
              margin: z.number(),
            })
          )
          .min(1, "At least one item is required"),
      })
    )
    .mutation(async ({ input }) => {
      return await db.estimate.create({
        data: {
          jobNumber: input.jobNumber,
          customer: input.customer,
          address: input.address,
          items: {
            create: input.items,
          },
        },
        include: { items: true },
      });
    }),
});
