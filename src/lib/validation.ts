import { z } from "zod";
import { useState, useCallback } from "react";

// Type for field errors
export type FieldErrors<T> = {
  [K in keyof T]?: string;
};

// Hook for managing field validation
export function useFieldValidation<T extends Record<string, unknown>>(
  schema: z.ZodType<T>
) {
  const [errors, setErrors] = useState<FieldErrors<T>>({});
  const [touched, setTouched] = useState<Set<keyof T>>(new Set());

  const validateField = useCallback((field: keyof T, value: unknown) => {
    // Clear any existing error for this field first
    setErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });

    // For now, we'll do basic validation and let the server handle full validation
    // This provides immediate feedback for obvious issues
    if (field === "jobNumber" || field === "customer") {
      if (!value || (typeof value === "string" && value.trim() === "")) {
        setErrors((prev) => ({
          ...prev,
          [field]: `${String(field)} is required`,
        }));
      }
    } else if (field === "address") {
      if (!value || (typeof value === "string" && value.trim() === "")) {
        setErrors((prev) => ({
          ...prev,
          [field]: "Address is required",
        }));
      }
    }
  }, []);

  const validateAllFields = useCallback(
    (data: T) => {
      try {
        schema.parse(data);
        setErrors({});
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          const newErrors: FieldErrors<T> = {};
          error.errors.forEach((err) => {
            const path = err.path[0] as keyof T;
            if (path && !newErrors[path]) {
              newErrors[path] = err.message;
            }
          });
          setErrors(newErrors);
        }
        return false;
      }
    },
    [schema]
  );

  const markFieldTouched = useCallback((field: keyof T) => {
    setTouched((prev) => new Set(prev).add(field));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
    setTouched(new Set());
  }, []);

  const getFieldError = useCallback(
    (field: keyof T) => {
      return touched.has(field) ? errors[field] : undefined;
    },
    [errors, touched]
  );

  return {
    errors,
    touched,
    validateField,
    validateAllFields,
    markFieldTouched,
    clearErrors,
    getFieldError,
    hasErrors: Object.keys(errors).length > 0,
  };
}

// Hook for validating arrays of items (like estimate items)
export function useArrayFieldValidation<T extends Record<string, unknown>>(
  itemSchema: z.ZodType<T>
) {
  const [errors, setErrors] = useState<Array<FieldErrors<T>>>([]);
  const [touched, setTouched] = useState<Array<Set<keyof T>>>([]);

  const validateItemField = useCallback(
    (itemIndex: number, field: keyof T, value: unknown) => {
      // Clear any existing error for this field first
      setErrors((prev) => {
        const newErrors = [...prev];
        if (newErrors[itemIndex]) {
          const itemErrors = { ...newErrors[itemIndex] };
          delete itemErrors[field];
          newErrors[itemIndex] = itemErrors;
        }
        return newErrors;
      });

      // Basic validation for estimate item fields
      if (field === "type" || field === "item") {
        if (!value || (typeof value === "string" && value.trim() === "")) {
          setErrors((prev) => {
            const newErrors = [...prev];
            newErrors[itemIndex] = {
              ...newErrors[itemIndex],
              [field]: `${String(field)} is required`,
            };
            return newErrors;
          });
        }
      } else if (field === "units") {
        if (typeof value === "number" && value < 1) {
          setErrors((prev) => {
            const newErrors = [...prev];
            newErrors[itemIndex] = {
              ...newErrors[itemIndex],
              [field]: "Units must be at least 1",
            };
            return newErrors;
          });
        }
      } else if (field === "rate" || field === "margin") {
        if (typeof value === "number" && value < 0) {
          setErrors((prev) => {
            const newErrors = [...prev];
            newErrors[itemIndex] = {
              ...newErrors[itemIndex],
              [field]: `${String(field)} must be positive`,
            };
            return newErrors;
          });
        }
        if (field === "margin" && typeof value === "number" && value > 100) {
          setErrors((prev) => {
            const newErrors = [...prev];
            newErrors[itemIndex] = {
              ...newErrors[itemIndex],
              [field]: "Margin cannot exceed 100%",
            };
            return newErrors;
          });
        }
      } else if (field === "time" && value !== undefined) {
        if (typeof value === "number" && value < 0) {
          setErrors((prev) => {
            const newErrors = [...prev];
            newErrors[itemIndex] = {
              ...newErrors[itemIndex],
              [field]: "Time must be positive",
            };
            return newErrors;
          });
        }
      }
    },
    []
  );

  const validateAllItems = useCallback(
    (items: T[]) => {
      const newErrors: Array<FieldErrors<T>> = [];
      let hasAnyErrors = false;

      items.forEach((item, index) => {
        try {
          itemSchema.parse(item);
          newErrors[index] = {};
        } catch (error) {
          if (error instanceof z.ZodError) {
            const itemErrors: FieldErrors<T> = {};
            error.errors.forEach((err) => {
              const path = err.path[0] as keyof T;
              if (path && !itemErrors[path]) {
                itemErrors[path] = err.message;
              }
            });
            newErrors[index] = itemErrors;
            hasAnyErrors = true;
          }
        }
      });

      setErrors(newErrors);
      return !hasAnyErrors;
    },
    [itemSchema]
  );

  const markItemFieldTouched = useCallback(
    (itemIndex: number, field: keyof T) => {
      setTouched((prev) => {
        const newTouched = [...prev];
        if (!newTouched[itemIndex]) {
          newTouched[itemIndex] = new Set();
        }
        newTouched[itemIndex].add(field);
        return newTouched;
      });
    },
    []
  );

  const getItemFieldError = useCallback(
    (itemIndex: number, field: keyof T) => {
      const itemTouched = touched[itemIndex];
      const itemErrors = errors[itemIndex];
      return itemTouched?.has(field) ? itemErrors?.[field] : undefined;
    },
    [errors, touched]
  );

  const clearAllErrors = useCallback(() => {
    setErrors([]);
    setTouched([]);
  }, []);

  const ensureItemExists = useCallback((itemIndex: number) => {
    setErrors((prev) => {
      const newErrors = [...prev];
      if (!newErrors[itemIndex]) {
        newErrors[itemIndex] = {};
      }
      return newErrors;
    });
    setTouched((prev) => {
      const newTouched = [...prev];
      if (!newTouched[itemIndex]) {
        newTouched[itemIndex] = new Set();
      }
      return newTouched;
    });
  }, []);

  return {
    errors,
    touched,
    validateItemField,
    validateAllItems,
    markItemFieldTouched,
    getItemFieldError,
    clearAllErrors,
    ensureItemExists,
    hasErrors: errors.some((itemErrors) => Object.keys(itemErrors).length > 0),
  };
}
