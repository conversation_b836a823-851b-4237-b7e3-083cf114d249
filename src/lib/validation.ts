import { z } from "zod";
import { useState, useCallback } from "react";

// Type for field errors
export type FieldErrors<T> = {
  [K in keyof T]?: string;
};

// Hook for managing field validation
export function useFieldValidation<T extends Record<string, unknown>>(
  schema: z.ZodObject<z.ZodRawShape>
) {
  const [errors, setErrors] = useState<FieldErrors<T>>({});
  const [touched, setTouched] = useState<Set<keyof T>>(new Set());

  const validateField = useCallback(
    (field: keyof T, value: unknown) => {
      try {
        // Try to validate just this field using the schema
        const testData = { [field]: value } as Partial<T>;
        schema.partial().parse(testData);

        // Clear error if validation passes
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldError = error.errors.find(
            (err) => err.path[0] === field
          )?.message;
          if (fieldError) {
            setErrors((prev) => ({
              ...prev,
              [field]: fieldError,
            }));
          }
        }
      }
    },
    [schema]
  );

  const validateAllFields = useCallback(
    (data: T) => {
      try {
        schema.parse(data);
        setErrors({});
        return true;
      } catch (error) {
        if (error instanceof z.ZodError) {
          const newErrors: FieldErrors<T> = {};
          error.errors.forEach((err) => {
            const path = err.path[0] as keyof T;
            if (path && !newErrors[path]) {
              newErrors[path] = err.message;
            }
          });
          setErrors(newErrors);
        }
        return false;
      }
    },
    [schema]
  );

  const markFieldTouched = useCallback((field: keyof T) => {
    setTouched((prev) => new Set(prev).add(field));
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
    setTouched(new Set());
  }, []);

  const getFieldError = useCallback(
    (field: keyof T) => {
      return touched.has(field) ? errors[field] : undefined;
    },
    [errors, touched]
  );

  return {
    errors,
    touched,
    validateField,
    validateAllFields,
    markFieldTouched,
    clearErrors,
    getFieldError,
    hasErrors: Object.keys(errors).length > 0,
  };
}

// Hook for validating arrays of items (like estimate items)
export function useArrayFieldValidation<T extends Record<string, unknown>>(
  itemSchema: z.ZodObject<z.ZodRawShape>
) {
  const [errors, setErrors] = useState<Array<FieldErrors<T>>>([]);
  const [touched, setTouched] = useState<Array<Set<keyof T>>>([]);

  const validateItemField = useCallback(
    (itemIndex: number, field: keyof T, value: unknown) => {
      try {
        // Try to validate just this field using the schema
        const testData = { [field]: value } as Partial<T>;
        itemSchema.partial().parse(testData);

        // Clear error if validation passes
        setErrors((prev) => {
          const newErrors = [...prev];
          if (newErrors[itemIndex]) {
            const itemErrors = { ...newErrors[itemIndex] };
            delete itemErrors[field];
            newErrors[itemIndex] = itemErrors;
          }
          return newErrors;
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldError = error.errors.find(
            (err) => err.path[0] === field
          )?.message;
          if (fieldError) {
            setErrors((prev) => {
              const newErrors = [...prev];
              newErrors[itemIndex] = {
                ...newErrors[itemIndex],
                [field]: fieldError,
              };
              return newErrors;
            });
          }
        }
      }
    },
    [itemSchema]
  );

  const validateAllItems = useCallback(
    (items: T[]) => {
      const newErrors: Array<FieldErrors<T>> = [];
      let hasAnyErrors = false;

      items.forEach((item, index) => {
        try {
          itemSchema.parse(item);
          newErrors[index] = {};
        } catch (error) {
          if (error instanceof z.ZodError) {
            const itemErrors: FieldErrors<T> = {};
            error.errors.forEach((err) => {
              const path = err.path[0] as keyof T;
              if (path && !itemErrors[path]) {
                itemErrors[path] = err.message;
              }
            });
            newErrors[index] = itemErrors;
            hasAnyErrors = true;
          }
        }
      });

      setErrors(newErrors);
      return !hasAnyErrors;
    },
    [itemSchema]
  );

  const markItemFieldTouched = useCallback(
    (itemIndex: number, field: keyof T) => {
      setTouched((prev) => {
        const newTouched = [...prev];
        if (!newTouched[itemIndex]) {
          newTouched[itemIndex] = new Set();
        }
        newTouched[itemIndex].add(field);
        return newTouched;
      });
    },
    []
  );

  const getItemFieldError = useCallback(
    (itemIndex: number, field: keyof T) => {
      const itemTouched = touched[itemIndex];
      const itemErrors = errors[itemIndex];
      return itemTouched?.has(field) ? itemErrors?.[field] : undefined;
    },
    [errors, touched]
  );

  const clearAllErrors = useCallback(() => {
    setErrors([]);
    setTouched([]);
  }, []);

  const ensureItemExists = useCallback((itemIndex: number) => {
    setErrors((prev) => {
      const newErrors = [...prev];
      if (!newErrors[itemIndex]) {
        newErrors[itemIndex] = {};
      }
      return newErrors;
    });
    setTouched((prev) => {
      const newTouched = [...prev];
      if (!newTouched[itemIndex]) {
        newTouched[itemIndex] = new Set();
      }
      return newTouched;
    });
  }, []);

  return {
    errors,
    touched,
    validateItemField,
    validateAllItems,
    markItemFieldTouched,
    getItemFieldError,
    clearAllErrors,
    ensureItemExists,
    hasErrors: errors.some((itemErrors) => Object.keys(itemErrors).length > 0),
  };
}
