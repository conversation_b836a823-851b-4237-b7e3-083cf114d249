import { z } from "zod";

// Item type enum
export const ItemType = z.enum(["LABOR", "MATERIALS", "EQUIPMENT"], {
  errorMap: () => ({ message: "Type must be Labor, Materials, or Equipment" }),
});

// Infer the TypeScript type from the Zod enum
export type ItemTypeValue = z.infer<typeof ItemType>;

// Constants for type-safe usage in components
export const ITEM_TYPES = {
  LABOR: "LABOR" as const,
  MATERIALS: "MATERIALS" as const,
  EQUIPMENT: "EQUIPMENT" as const,
} satisfies Record<string, ItemTypeValue>;

// Helper to get display names
export const ITEM_TYPE_LABELS: Record<ItemTypeValue, string> = {
  LABOR: "Labor",
  MATERIALS: "Materials",
  EQUIPMENT: "Equipment",
};

// Validation schemas that can be shared between client and server
export const estimateItemSchema = z
  .object({
    type: ItemType,
    item: z.string().min(1, "Item description is required"),
    units: z.number().min(0.01, "Units must be greater than 0"),
    unitType: z.string().optional(), // Required for MATERIALS, auto-set for LABOR, not used for EQUIPMENT
    time: z.number().min(0, "Time must be positive").optional(),
    rate: z.number().min(0, "Rate must be positive"),
    marginPercent: z
      .number()
      .min(0, "Margin must be positive")
      .max(100, "Margin cannot exceed 100%"),
  })
  .refine(
    (data) => {
      // For MATERIALS, unitType is required
      if (
        data.type === "MATERIALS" &&
        (!data.unitType || data.unitType.trim() === "")
      ) {
        return false;
      }
      return true;
    },
    {
      message: "Unit type is required for materials",
      path: ["unitType"],
    }
  )
  .refine(
    (data) => {
      // For LABOR and EQUIPMENT, time is required
      if (
        (data.type === "LABOR" || data.type === "EQUIPMENT") &&
        (data.time === undefined || data.time === null || data.time <= 0)
      ) {
        return false;
      }
      return true;
    },
    {
      message: "Time is required for labor and equipment",
      path: ["time"],
    }
  );

export const estimateSchema = z.object({
  jobNumber: z.string().min(1, "Job number is required"),
  customer: z.string().min(1, "Customer is required"),
  address: z.string().min(1, "Address is required"),
  items: z.array(estimateItemSchema).min(1, "At least one item is required"),
});

// Type exports for convenience
export type EstimateItem = z.infer<typeof estimateItemSchema>;
export type Estimate = z.infer<typeof estimateSchema>;
