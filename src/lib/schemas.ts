import { z } from "zod";

// Item type enum
export const ItemType = z.enum(["LABOR", "MATERIALS", "EQUIPMENT"], {
  errorMap: () => ({ message: "Type must be Labor, Materials, or Equipment" }),
});

// Validation schemas that can be shared between client and server
export const estimateItemSchema = z.object({
  type: ItemType,
  item: z.string().min(1, "Item description is required"),
  units: z.number().min(0.01, "Units must be greater than 0"),
  time: z.number().min(0, "Time must be positive").optional(),
  rate: z.number().min(0, "Rate must be positive"),
  marginPercent: z
    .number()
    .min(0, "Margin must be positive")
    .max(100, "Margin cannot exceed 100%"),
});

export const estimateSchema = z.object({
  jobNumber: z.string().min(1, "Job number is required"),
  customer: z.string().min(1, "Customer is required"),
  address: z.string().min(1, "Address is required"),
  items: z.array(estimateItemSchema).min(1, "At least one item is required"),
});

// Type exports for convenience
export type EstimateItem = z.infer<typeof estimateItemSchema>;
export type Estimate = z.infer<typeof estimateSchema>;
