"use client";

import { useState } from "react";
import { inferRouterOutputs, inferRouterInputs } from "@trpc/server";
import { AppRouter } from "@/lib/trpc/routers/_app";
import { trpc } from "@/lib/trpc/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import EstimateItemForm from "@/components/EstimateItemForm";
import { estimateSchema, estimateItemSchema } from "@/lib/schemas";
import { useFieldValidation, useArrayFieldValidation } from "@/lib/validation";

type RouterOutput = inferRouterOutputs<AppRouter>;
type RouterInput = inferRouterInputs<AppRouter>;

type Estimate = RouterOutput["estimate"]["getAll"][number];
type NewEstimateItem = RouterInput["estimate"]["create"]["items"][number];

export default function EstimateClient() {
  const [jobNumber, setJobNumber] = useState("");
  const [customer, setCustomer] = useState("");
  const [address, setAddress] = useState("");
  const [items, setItems] = useState<NewEstimateItem[]>([
    {
      type: "LABOR",
      item: "",
      units: 0,
      time: undefined,
      rate: 0,
      marginPercent: 0,
    },
  ]);

  // Validation hooks
  const mainFormValidation = useFieldValidation(
    estimateSchema.omit({ items: true })
  );
  const itemsValidation =
    useArrayFieldValidation<NewEstimateItem>(estimateItemSchema);

  const utils = trpc.useUtils();
  const create = trpc.estimate.create.useMutation({
    onSuccess: () => {
      setJobNumber("");
      setCustomer("");
      setAddress("");
      setItems([
        {
          type: "LABOR",
          item: "",
          units: 0,
          time: undefined,
          rate: 0,
          marginPercent: 0,
        },
      ]);
      mainFormValidation.clearErrors();
      itemsValidation.clearAllErrors();
      utils.estimate.getAll.invalidate();
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate main form fields
    const mainFormData = { jobNumber, customer, address };
    const isMainFormValid = mainFormValidation.validateAllFields(mainFormData);

    // Validate all items
    const areItemsValid = itemsValidation.validateAllItems(items);

    // Only submit if all validation passes
    if (isMainFormValid && areItemsValid) {
      create.mutate({ jobNumber, customer, address, items });
    }
  };

  const handleMainFieldChange = (
    field: "jobNumber" | "customer" | "address",
    value: string
  ) => {
    // Update state
    if (field === "jobNumber") setJobNumber(value);
    else if (field === "customer") setCustomer(value);
    else if (field === "address") setAddress(value);

    // Validate field
    mainFormValidation.validateField(field, value);
  };

  const handleMainFieldBlur = (field: "jobNumber" | "customer" | "address") => {
    mainFormValidation.markFieldTouched(field);
  };

  const query = trpc.estimate.getAll.useQuery();

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Create Estimate</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          value={jobNumber}
          onChange={(e) => handleMainFieldChange("jobNumber", e.target.value)}
          onBlur={() => handleMainFieldBlur("jobNumber")}
          placeholder="Job #"
          error={mainFormValidation.getFieldError("jobNumber")}
        />
        <Input
          value={customer}
          onChange={(e) => handleMainFieldChange("customer", e.target.value)}
          onBlur={() => handleMainFieldBlur("customer")}
          placeholder="Customer"
          error={mainFormValidation.getFieldError("customer")}
        />
        <Input
          value={address}
          onChange={(e) => handleMainFieldChange("address", e.target.value)}
          onBlur={() => handleMainFieldBlur("address")}
          placeholder="Address"
          error={mainFormValidation.getFieldError("address")}
        />

        <div className="space-y-2">
          <h3 className="font-semibold">Estimate Items</h3>
          {items.map((itm, i) => (
            <EstimateItemForm
              key={i}
              index={i}
              item={itm}
              onChange={(index, updated) => {
                const copy = [...items];
                copy[index] = updated;
                setItems(copy);
              }}
              validation={itemsValidation}
            />
          ))}

          <Button
            type="button"
            variant="secondary"
            onClick={() =>
              setItems([
                ...items,
                {
                  type: "LABOR",
                  item: "",
                  units: 0,
                  time: undefined,
                  rate: 0,
                  marginPercent: 0,
                },
              ])
            }
          >
            + Add Item
          </Button>
        </div>

        <Button
          type="submit"
          disabled={
            create.isPending ||
            mainFormValidation.hasErrors ||
            itemsValidation.hasErrors
          }
        >
          {create.isPending ? "Saving..." : "Save"}
        </Button>

        {create.error && (
          <div className="text-sm text-destructive font-medium">
            Error: {create.error.message}
          </div>
        )}
      </form>

      <h2 className="text-xl font-semibold mt-6">All Estimates</h2>
      {query.isLoading ? (
        <p>Loading...</p>
      ) : (
        <ul className="space-y-4">
          {query.data?.map((est: Estimate) => (
            <li key={est.id} className="border p-4 rounded">
              <div>
                <strong>{est.jobNumber}</strong>
              </div>
              <div>
                {est.customer} — {est.address}
              </div>
              <ul className="mt-2 text-sm space-y-1 text-gray-700">
                {est.items.map((itm: Estimate["items"][number]) => (
                  <li key={itm.id}>
                    {itm.type}: {itm.item}, {itm.units} units @ ${itm.rate},
                    margin: {itm.marginPercent}%
                    {itm.time !== null && `, time: ${itm.time}h`}
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
