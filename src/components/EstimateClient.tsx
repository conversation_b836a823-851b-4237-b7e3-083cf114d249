"use client";

import { useState } from "react";
import { inferRouterOutputs, inferRouterInputs } from "@trpc/server";
import { AppRouter } from "@/lib/trpc/routers/_app";
import { trpc } from "@/lib/trpc/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import EstimateItemForm from "@/components/EstimateItemForm";

type RouterOutput = inferRouterOutputs<AppRouter>;
type RouterInput = inferRouterInputs<AppRouter>;

type Estimate = RouterOutput["estimate"]["getAll"][number];
type NewEstimateItem = RouterInput["estimate"]["create"]["items"][number];

export default function EstimateClient() {
  const [jobNumber, setJobNumber] = useState("");
  const [customer, setCustomer] = useState("");
  const [address, setAddress] = useState("");
  const [items, setItems] = useState<NewEstimateItem[]>([
    { type: "", item: "", units: 0, time: undefined, rate: 0, margin: 0 },
  ]);

  const utils = trpc.useUtils();
  const create = trpc.estimate.create.useMutation({
    onSuccess: () => {
      setJobNumber("");
      setCustomer("");
      setAddress("");
      setItems([
        { type: "", item: "", units: 0, time: undefined, rate: 0, margin: 0 },
      ]);
      utils.estimate.getAll.invalidate();
    },
  });

  const query = trpc.estimate.getAll.useQuery();

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Create Estimate</h2>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          create.mutate({ jobNumber, customer, address, items });
        }}
        className="space-y-4"
      >
        <Input
          value={jobNumber}
          onChange={(e) => setJobNumber(e.target.value)}
          placeholder="Job #"
        />
        <Input
          value={customer}
          onChange={(e) => setCustomer(e.target.value)}
          placeholder="Customer"
        />
        <Input
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          placeholder="Address"
        />

        <div className="space-y-2">
          <h3 className="font-semibold">Estimate Items</h3>
          {items.map((itm, i) => (
            <EstimateItemForm
              key={i}
              index={i}
              item={itm}
              onChange={(index, updated) => {
                const copy = [...items];
                copy[index] = updated;
                setItems(copy);
              }}
            />
          ))}

          <Button
            type="button"
            variant="secondary"
            onClick={() =>
              setItems([
                ...items,
                {
                  type: "",
                  item: "",
                  units: 0,
                  time: undefined,
                  rate: 0,
                  margin: 0,
                },
              ])
            }
          >
            + Add Item
          </Button>
        </div>

        <Button type="submit">Save</Button>
      </form>

      <h2 className="text-xl font-semibold mt-6">All Estimates</h2>
      {query.isLoading ? (
        <p>Loading...</p>
      ) : (
        <ul className="space-y-4">
          {query.data?.map((est: Estimate) => (
            <li key={est.id} className="border p-4 rounded">
              <div>
                <strong>{est.jobNumber}</strong>
              </div>
              <div>
                {est.customer} — {est.address}
              </div>
              <ul className="mt-2 text-sm space-y-1 text-gray-700">
                {est.items.map((itm: Estimate["items"][number]) => (
                  <li key={itm.id}>
                    {itm.type}: {itm.item}, {itm.units} units @ ${itm.rate},
                    margin: {itm.margin}%
                    {itm.time !== null && `, time: ${itm.time}h`}
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
