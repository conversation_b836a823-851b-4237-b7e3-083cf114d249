"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { inferRouterInputs } from "@trpc/server";
import { AppRouter } from "@/lib/trpc/routers/_app";
import { useArrayFieldValidation } from "@/lib/validation";
import {
  ITEM_TYPES,
  ITEM_TYPE_LABELS,
  type ItemTypeValue,
} from "@/lib/schemas";

type RouterInput = inferRouterInputs<AppRouter>;
type NewEstimateItem = RouterInput["estimate"]["create"]["items"][number];

type Props = {
  index: number;
  item: NewEstimateItem;
  onChange: (index: number, updated: NewEstimateItem) => void;
  validation?: ReturnType<typeof useArrayFieldValidation<NewEstimateItem>>;
};

export default function EstimateItemForm({
  index,
  item,
  onChange,
  validation,
}: Props) {
  const updateField = <K extends keyof NewEstimateItem>(
    key: K,
    value: NewEstimateItem[K]
  ) => {
    let updatedItem = { ...item, [key]: value };

    // Auto-set unitType based on type
    if (key === "type") {
      if (value === ITEM_TYPES.LABOR) {
        updatedItem.unitType = "crew";
      } else if (value === ITEM_TYPES.EQUIPMENT) {
        updatedItem.unitType = undefined;
      }
      // For MATERIALS, keep existing unitType or clear it if switching from other types
    }

    onChange(index, updatedItem);
    // Validate the field if validation is provided
    if (validation) {
      validation.ensureItemExists(index);
      validation.validateItemField(index, key, value);
    }
  };

  const handleBlur = (field: keyof NewEstimateItem) => {
    if (validation) {
      validation.markItemFieldTouched(index, field);
    }
  };

  return (
    <div className="border p-4 rounded space-y-2">
      <div>
        <Label>Type</Label>
        <Select
          value={item.type}
          onValueChange={(value) => {
            updateField("type", value as ItemTypeValue);
            handleBlur("type");
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={ITEM_TYPES.LABOR}>
              {ITEM_TYPE_LABELS[ITEM_TYPES.LABOR]}
            </SelectItem>
            <SelectItem value={ITEM_TYPES.MATERIALS}>
              {ITEM_TYPE_LABELS[ITEM_TYPES.MATERIALS]}
            </SelectItem>
            <SelectItem value={ITEM_TYPES.EQUIPMENT}>
              {ITEM_TYPE_LABELS[ITEM_TYPES.EQUIPMENT]}
            </SelectItem>
          </SelectContent>
        </Select>
        {validation?.getItemFieldError(index, "type") && (
          <p className="text-sm text-destructive font-medium">
            {validation.getItemFieldError(index, "type")}
          </p>
        )}
      </div>

      <div>
        <Label>Item</Label>
        <Input
          value={item.item}
          onChange={(e) => updateField("item", e.target.value)}
          onBlur={() => handleBlur("item")}
          placeholder="Item description"
          error={validation?.getItemFieldError(index, "item")}
        />
      </div>

      {/* Units section - conditional based on type */}
      {item.type === ITEM_TYPES.LABOR ? (
        // Labor: Units only (always "crew")
        <div>
          <Label>Crew Size</Label>
          <Input
            type="number"
            min="1"
            step="1"
            value={item.units}
            onChange={(e) => updateField("units", Number(e.target.value) || 0)}
            onBlur={() => handleBlur("units")}
            placeholder="Number of crew members"
            error={validation?.getItemFieldError(index, "units")}
          />
        </div>
      ) : item.type === ITEM_TYPES.MATERIALS ? (
        // Materials: Units + Unit Type (required)
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label>Quantity</Label>
            <Input
              type="number"
              min="0.01"
              step="0.01"
              value={item.units}
              onChange={(e) =>
                updateField("units", Number(e.target.value) || 0)
              }
              onBlur={() => handleBlur("units")}
              placeholder="Amount"
              error={validation?.getItemFieldError(index, "units")}
            />
          </div>
          <div>
            <Label>Unit Type *</Label>
            <Input
              value={item.unitType || ""}
              onChange={(e) =>
                updateField("unitType", e.target.value || undefined)
              }
              onBlur={() => handleBlur("unitType")}
              placeholder="tons, gallons, cubic yards..."
              error={validation?.getItemFieldError(index, "unitType")}
            />
          </div>
        </div>
      ) : (
        // Equipment: Units only (no unit type needed)
        <div>
          <Label>Quantity</Label>
          <Input
            type="number"
            min="1"
            step="1"
            value={item.units}
            onChange={(e) => updateField("units", Number(e.target.value) || 0)}
            onBlur={() => handleBlur("units")}
            placeholder="Number of units"
            error={validation?.getItemFieldError(index, "units")}
          />
        </div>
      )}

      <div>
        <Label>Time (optional)</Label>
        <Input
          type="number"
          min="0"
          step="0.1"
          value={item.time ?? ""}
          onChange={(e) =>
            updateField(
              "time",
              e.target.value ? Number(e.target.value) : undefined
            )
          }
          onBlur={() => handleBlur("time")}
          placeholder="Time in hours"
          error={validation?.getItemFieldError(index, "time")}
        />
      </div>

      <div>
        <Label>
          Rate{" "}
          {item.time && item.time > 0
            ? "($ per hr)"
            : item.type === ITEM_TYPES.LABOR
            ? "($ per hr)"
            : item.type === ITEM_TYPES.MATERIALS && item.unitType
            ? `($ per ${item.unitType})`
            : item.type === ITEM_TYPES.EQUIPMENT
            ? "($ per hr)"
            : "($ per unit)"}
        </Label>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={item.rate}
          onChange={(e) => updateField("rate", Number(e.target.value) || 0)}
          onBlur={() => handleBlur("rate")}
          placeholder={
            item.time && item.time > 0
              ? "Rate per hour"
              : item.type === ITEM_TYPES.LABOR
              ? "Rate per hour"
              : item.type === ITEM_TYPES.MATERIALS && item.unitType
              ? `Rate per ${item.unitType}`
              : item.type === ITEM_TYPES.EQUIPMENT
              ? "Rate per hour"
              : "Rate per unit"
          }
          error={validation?.getItemFieldError(index, "rate")}
        />
      </div>

      <div>
        <Label>Margin</Label>
        <Input
          type="number"
          min="0"
          max="100"
          step="0.1"
          value={item.marginPercent}
          onChange={(e) =>
            updateField("marginPercent", Number(e.target.value) || 0)
          }
          onBlur={() => handleBlur("marginPercent")}
          placeholder="Margin percentage (%)"
          error={validation?.getItemFieldError(index, "marginPercent")}
        />
      </div>
    </div>
  );
}
