"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { inferRouterInputs } from "@trpc/server";
import { AppRouter } from "@/lib/trpc/routers/_app";
import { useArrayFieldValidation } from "@/lib/validation";
import {
  ITEM_TYPES,
  ITEM_TYPE_LABELS,
  type ItemTypeValue,
} from "@/lib/schemas";

type RouterInput = inferRouterInputs<AppRouter>;
type NewEstimateItem = RouterInput["estimate"]["create"]["items"][number];

type Props = {
  index: number;
  item: NewEstimateItem;
  onChange: (index: number, updated: NewEstimateItem) => void;
  validation?: ReturnType<typeof useArrayFieldValidation<NewEstimateItem>>;
};

export default function EstimateItemForm({
  index,
  item,
  onChange,
  validation,
}: Props) {
  const updateField = <K extends keyof NewEstimateItem>(
    key: K,
    value: NewEstimateItem[K]
  ) => {
    onChange(index, { ...item, [key]: value });
    // Validate the field if validation is provided
    if (validation) {
      validation.ensureItemExists(index);
      validation.validateItemField(index, key, value);
    }
  };

  const handleBlur = (field: keyof NewEstimateItem) => {
    if (validation) {
      validation.markItemFieldTouched(index, field);
    }
  };

  return (
    <div className="border p-4 rounded space-y-2">
      <div>
        <Label>Type</Label>
        <Select
          value={item.type}
          onChange={(e) => updateField("type", e.target.value as ItemTypeValue)}
          onBlur={() => handleBlur("type")}
          error={validation?.getItemFieldError(index, "type")}
        >
          <option value={ITEM_TYPES.LABOR}>
            {ITEM_TYPE_LABELS[ITEM_TYPES.LABOR]}
          </option>
          <option value={ITEM_TYPES.MATERIALS}>
            {ITEM_TYPE_LABELS[ITEM_TYPES.MATERIALS]}
          </option>
          <option value={ITEM_TYPES.EQUIPMENT}>
            {ITEM_TYPE_LABELS[ITEM_TYPES.EQUIPMENT]}
          </option>
        </Select>
      </div>

      <div>
        <Label>Item</Label>
        <Input
          value={item.item}
          onChange={(e) => updateField("item", e.target.value)}
          onBlur={() => handleBlur("item")}
          placeholder="Item description"
          error={validation?.getItemFieldError(index, "item")}
        />
      </div>

      <div>
        <Label>Units</Label>
        <Input
          type="number"
          min="1"
          value={item.units}
          onChange={(e) => updateField("units", Number(e.target.value) || 0)}
          onBlur={() => handleBlur("units")}
          placeholder="Number of units"
          error={validation?.getItemFieldError(index, "units")}
        />
      </div>

      <div>
        <Label>Time (optional)</Label>
        <Input
          type="number"
          min="0"
          step="0.1"
          value={item.time ?? ""}
          onChange={(e) =>
            updateField(
              "time",
              e.target.value ? Number(e.target.value) : undefined
            )
          }
          onBlur={() => handleBlur("time")}
          placeholder="Time in hours"
          error={validation?.getItemFieldError(index, "time")}
        />
      </div>

      <div>
        <Label>Rate</Label>
        <Input
          type="number"
          min="0"
          step="0.01"
          value={item.rate}
          onChange={(e) => updateField("rate", Number(e.target.value) || 0)}
          onBlur={() => handleBlur("rate")}
          placeholder="Rate per unit ($)"
          error={validation?.getItemFieldError(index, "rate")}
        />
      </div>

      <div>
        <Label>Margin</Label>
        <Input
          type="number"
          min="0"
          max="100"
          step="0.1"
          value={item.marginPercent}
          onChange={(e) =>
            updateField("marginPercent", Number(e.target.value) || 0)
          }
          onBlur={() => handleBlur("marginPercent")}
          placeholder="Margin percentage (%)"
          error={validation?.getItemFieldError(index, "marginPercent")}
        />
      </div>
    </div>
  );
}
