'use client';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { inferRouterInputs } from '@trpc/server';
import { AppRouter } from '@/lib/trpc/routers/_app';

type RouterInput = inferRouterInputs<AppRouter>;
type NewEstimateItem = RouterInput['estimate']['create']['items'][number];

type Props = {
  index: number;
  item: NewEstimateItem;
  onChange: (index: number, updated: NewEstimateItem) => void;
};

export default function EstimateItemForm({ index, item, onChange }: Props) {
  const updateField = <K extends keyof NewEstimateItem>(key: K, value: NewEstimateItem[K]) => {
    onChange(index, { ...item, [key]: value });
  };

  return (
    <div className="border p-4 rounded space-y-2">
      <div>
        <Label>Type</Label>
        <Input
          value={item.type}
          onChange={(e) => updateField('type', e.target.value)}
          placeholder="Type"
        />
      </div>

      <div>
        <Label>Item</Label>
        <Input
          value={item.item}
          onChange={(e) => updateField('item', e.target.value)}
          placeholder="Item"
        />
      </div>

      <div>
        <Label>Units</Label>
        <Input
          type="number"
          value={item.units}
          onChange={(e) => updateField('units', Number(e.target.value))}
          placeholder="Units"
        />
      </div>

      <div>
        <Label>Time (optional)</Label>
        <Input
          type="number"
          value={item.time ?? ''}
          onChange={(e) =>
            updateField('time', e.target.value ? Number(e.target.value) : undefined)
          }
          placeholder="Time"
        />
      </div>

      <div>
        <Label>Rate</Label>
        <Input
          type="number"
          value={item.rate}
          onChange={(e) => updateField('rate', Number(e.target.value))}
          placeholder="Rate"
        />
      </div>

      <div>
        <Label>Margin</Label>
        <Input
          type="number"
          value={item.margin}
          onChange={(e) => updateField('margin', Number(e.target.value))}
          placeholder="Margin"
        />
      </div>
    </div>
  );
}
