{"name": "estimates-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.10.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.5", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "class-variance-authority": "^0.7.1", "client-only": "^0.0.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.4", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "server-only": "^0.0.1", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}