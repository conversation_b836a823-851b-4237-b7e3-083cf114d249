// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Estimate {
  id         Int             @id @default(autoincrement())
  jobNumber  String
  customer   String
  address    String
  createdAt  DateTime        @default(now())
  items      EstimateItem[]
}

model EstimateItem {
  id         Int      @id @default(autoincrement())
  type       String   // e.g., labor, material, equipment
  item       String
  units      Int
  time       Int?     // optional
  rate       Float
  margin     Float
  estimate   Estimate @relation(fields: [estimateId], references: [id])
  estimateId Int
}
