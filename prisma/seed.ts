import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Sample data for realistic estimates
const customers = [
  { name: "Max Kostow", address: "44 Tehama Street, San Francisco, CA" },
  { name: "<PERSON>", address: "123 Main St, Oakland, CA" },
  { name: "<PERSON>", address: "456 Oak Ave, Berkeley, CA" },
  { name: "<PERSON>", address: "789 Pine St, San Jose, CA" },
  { name: "<PERSON>", address: "321 Elm Dr, Palo Alto, CA" },
  { name: "<PERSON>", address: "654 Maple Ln, Fremont, CA" },
  { name: "<PERSON>", address: "987 Cedar Rd, Hayward, CA" },
  { name: "<PERSON>", address: "147 Birch St, Mountain View, CA" },
];

const laborItems = [
  { item: "Excavation", baseRate: 35, timeRange: [2, 8] },
  { item: "Grading", baseRate: 30, timeRange: [3, 6] },
  { item: "Paving", baseRate: 40, timeRange: [4, 10] },
  { item: "Compaction", baseRate: 25, timeRange: [1, 4] },
  { item: "Line Striping", baseRate: 45, timeRange: [2, 5] },
];

const materialItems = [
  { item: "Asphalt", unitType: "tons", baseRate: 75, unitsRange: [50, 200] },
  {
    item: "Concrete",
    unitType: "cubic yards",
    baseRate: 120,
    unitsRange: [10, 50],
  },
  {
    item: "Gravel Base",
    unitType: "tons",
    baseRate: 35,
    unitsRange: [20, 100],
  },
  {
    item: "Sealcoating",
    unitType: "gallons",
    baseRate: 35,
    unitsRange: [5, 50],
  },
  {
    item: "Crack Filler",
    unitType: "gallons",
    baseRate: 25,
    unitsRange: [2, 20],
  },
  { item: "Sand", unitType: "cubic yards", baseRate: 45, unitsRange: [10, 80] },
  { item: "Stone", unitType: "tons", baseRate: 55, unitsRange: [15, 120] },
  { item: "Emulsion", unitType: "gallons", baseRate: 28, unitsRange: [3, 30] },
  { item: "Rebar", unitType: "pounds", baseRate: 2.5, unitsRange: [100, 500] },
];

const equipmentItems = [
  { item: "Excavator", baseRate: 150, timeRange: [4, 8] },
  { item: "Paver", baseRate: 700, timeRange: [6, 12] },
  { item: "Roller", baseRate: 80, timeRange: [2, 6] },
  { item: "Dump Truck", baseRate: 120, timeRange: [4, 8] },
  { item: "Bobcat", baseRate: 60, timeRange: [2, 6] },
];

function randomBetween(min: number, max: number): number {
  return Math.random() * (max - min) + min;
}

function randomInt(min: number, max: number): number {
  return Math.floor(randomBetween(min, max));
}

function randomChoice<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function generateJobNumber(): string {
  const year = new Date().getFullYear();
  const num = Math.floor(Math.random() * 999) + 1;
  return `${year}-${num.toString().padStart(3, "0")}`;
}

function generateEstimateItems() {
  const items = [];
  const numItems = randomInt(3, 8); // 3-7 items per estimate

  // Always include at least one labor item
  const laborItem = randomChoice(laborItems);
  const laborUnits = randomInt(1, 5);
  const laborTime = randomBetween(
    laborItem.timeRange[0],
    laborItem.timeRange[1]
  );
  items.push({
    type: "LABOR" as const,
    item: laborItem.item,
    units: laborUnits,
    unitType: "crew", // Always "crew" for labor
    time: Math.round(laborTime * 10) / 10, // Round to 1 decimal
    rate: laborItem.baseRate + randomInt(-5, 15), // Add some variation
    marginPercent: randomInt(20, 40),
  });

  // Add 1-3 material items
  const numMaterials = randomInt(1, 4);
  for (let i = 0; i < numMaterials; i++) {
    const materialItem = randomChoice(materialItems);
    const units = randomBetween(
      materialItem.unitsRange[0],
      materialItem.unitsRange[1]
    );
    items.push({
      type: "MATERIALS" as const,
      item: materialItem.item,
      units: Math.round(units * 10) / 10,
      unitType: materialItem.unitType,
      time: undefined,
      rate: materialItem.baseRate + randomInt(-10, 20),
      marginPercent: randomInt(15, 30),
    });
  }

  // Add 0-2 equipment items
  const numEquipment = randomInt(0, 3);
  for (let i = 0; i < numEquipment; i++) {
    const equipmentItem = randomChoice(equipmentItems);
    const equipmentUnits = randomInt(1, 3);
    const equipmentTime = randomBetween(
      equipmentItem.timeRange[0],
      equipmentItem.timeRange[1]
    );
    items.push({
      type: "EQUIPMENT" as const,
      item: equipmentItem.item,
      units: equipmentUnits,
      unitType: undefined, // No unit type for equipment
      time: Math.round(equipmentTime * 10) / 10,
      rate: equipmentItem.baseRate + randomInt(-20, 50),
      marginPercent: randomInt(15, 25),
    });
  }

  return items.slice(0, numItems); // Ensure we don't exceed numItems
}

async function main() {
  console.log("🌱 Starting seed...");

  // Clear existing data
  await prisma.estimateItem.deleteMany();
  await prisma.estimate.deleteMany();

  // Generate estimates
  const estimates = [];
  for (let i = 0; i < 15; i++) {
    const customer = randomChoice(customers);
    const estimate = await prisma.estimate.create({
      data: {
        jobNumber: generateJobNumber(),
        customer: customer.name,
        address: customer.address,
        items: {
          create: generateEstimateItems(),
        },
      },
      include: {
        items: true,
      },
    });
    estimates.push(estimate);
  }

  console.log(`✅ Created ${estimates.length} estimates with items`);

  // Log summary
  const totalItems = estimates.reduce((sum, est) => sum + est.items.length, 0);
  console.log(`📊 Total items: ${totalItems}`);
  console.log(
    `📋 Average items per estimate: ${(totalItems / estimates.length).toFixed(
      1
    )}`
  );
}

main()
  .catch((e) => {
    console.error("❌ Seed failed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
